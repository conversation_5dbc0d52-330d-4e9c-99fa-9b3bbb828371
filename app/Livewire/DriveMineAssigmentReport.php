<?php

namespace App\Livewire;

use App\Models\Mine;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Livewire\Component;

class DriveMineAssigmentReport extends Component  implements HasForms
{
    use InteractsWithForms;


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('mine_id')
                    ->label('Mina')
                    ->options(Mine::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->preload()
                    ->native(false),
            ]);
    }
    public function render()
    {
        return view('livewire.drive-mine-assigment-report');
    }
}
